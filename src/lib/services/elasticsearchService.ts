import { esClient } from '@/lib/elasticsearch';

/**
 * ES搜索结果接口
 */
export interface ESSearchHit {
  id: string;
  table_code: string;
  registration_no?: string;
  product_combined?: string;
  company_combined?: string;
}

/**
 * ES搜索响应接口
 */
export interface ESSearchResponse {
  hits: ESSearchHit[];
  total: number;
  took: number;
}

/**
 * ES搜索参数接口
 */
export interface ESSearchParams {
  query: string;
  table_code?: string; // 限定特定数据库
  from?: number;
  size?: number;
  sort?: Array<{ [key: string]: 'asc' | 'desc' }>;
}

/**
 * Elasticsearch搜索服务
 * 负责ES查询逻辑，返回标准化的搜索结果
 */
export class ElasticsearchService {
  private static readonly INDEX_NAME = 'medical_index';
  
  /**
   * 执行ES搜索
   * @param params 搜索参数
   * @returns ES搜索结果
   */
  static async search(params: ESSearchParams): Promise<ESSearchResponse> {
    const { query, table_code, from = 0, size = 20, sort } = params;
    
    try {
      console.log('[ElasticsearchService] 执行搜索:', { query, table_code, from, size });
      
      // 构建查询条件
      const mustQueries = [];
      
      // 限定数据库
      if (table_code) {
        mustQueries.push({
          term: { 'table_code': table_code }
        });
      }
      
      // 构建搜索体
      const searchBody: any = {
        query: {
          bool: {
            must: mustQueries,
            should: [
              // 精确短语匹配（最高权重）
              {
                multi_match: {
                  query: query,
                  fields: [
                    'registration_no.raw^5',
                    'product_combined^3',
                    'company_combined^3'
                  ],
                  type: 'phrase',
                  boost: 5
                }
              },
              // 包含匹配（中等权重）
              {
                multi_match: {
                  query: query,
                  fields: [
                    'registration_no.*^2',
                    'product_combined.*^2',
                    'company_combined.*^2'
                  ],
                  type: 'cross_fields',
                  operator: 'and',
                  boost: 2
                }
              },
              // 通配符匹配（用于部分匹配）
              {
                bool: {
                  should: [
                    { wildcard: { 'registration_no.raw': `*${query.toLowerCase()}*` } },
                    { wildcard: { 'product_combined': `*${query.toLowerCase()}*` } },
                    { wildcard: { 'company_combined': `*${query.toLowerCase()}*` } }
                  ],
                  boost: 1
                }
              }
            ],
            minimum_should_match: 1
          }
        },
        from,
        size,
        track_total_hits: true,
        _source: ['id', 'table_code', 'registration_no', 'product_combined', 'company_combined']
      };
      
      // 添加排序
      if (sort && sort.length > 0) {
        searchBody.sort = sort;
      } else {
        // 默认按相关性排序
        searchBody.sort = [{ '_score': 'desc' }];
      }
      
      console.log('[ElasticsearchService] 搜索体:', JSON.stringify(searchBody, null, 2));
      
      const response = await esClient.search({
        index: this.INDEX_NAME,
        body: searchBody
      });
      
      const hits = response.hits.hits.map((hit: any) => ({
        id: hit._source.id,
        table_code: hit._source.table_code,
        registration_no: hit._source.registration_no,
        product_combined: hit._source.product_combined,
        company_combined: hit._source.company_combined
      }));
      
      const result = {
        hits,
        total: typeof response.hits.total === 'object' ? response.hits.total.value : response.hits.total,
        took: response.took
      };
      
      console.log('[ElasticsearchService] 搜索结果:', { 
        hitCount: hits.length, 
        total: result.total, 
        took: result.took 
      });
      
      return result;
      
    } catch (error) {
      console.error('[ElasticsearchService] 搜索失败:', error);
      throw new Error(`ES搜索失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }
  
  /**
   * 按数据库分组统计搜索结果
   * @param query 搜索关键词
   * @param table_codes 要搜索的数据库代码列表
   * @returns 每个数据库的匹配数量
   */
  static async searchByDatabases(
    query: string, 
    table_codes: string[]
  ): Promise<Array<{ database: string; count: number }>> {
    try {
      console.log('[ElasticsearchService] 按数据库分组搜索:', { query, table_codes });
      
      // 为每个数据库代码单独执行搜索查询
      const searchPromises = table_codes.map(async (code) => {
        try {
          const result = await this.search({
            query,
            table_code: code,
            from: 0,
            size: 0 // 只要统计数量，不要具体结果
          });
          
          return {
            database: code,
            count: result.total
          };
        } catch (error) {
          console.warn(`[ElasticsearchService] 数据库 ${code} 搜索失败:`, error);
          return {
            database: code,
            count: 0
          };
        }
      });
      
      const results = await Promise.all(searchPromises);
      
      console.log('[ElasticsearchService] 分组搜索结果:', results);
      
      return results;
      
    } catch (error) {
      console.error('[ElasticsearchService] 分组搜索失败:', error);
      throw new Error(`ES分组搜索失败: ${error instanceof Error ? error.message : String(error)}`);
    }
  }
  
  /**
   * 检查ES连接状态
   * @returns 是否连接正常
   */
  static async checkHealth(): Promise<boolean> {
    try {
      const response = await esClient.ping();
      return response.statusCode === 200;
    } catch (error) {
      console.error('[ElasticsearchService] 健康检查失败:', error);
      return false;
    }
  }
  
  /**
   * 检查索引是否存在
   * @returns 索引是否存在
   */
  static async checkIndexExists(): Promise<boolean> {
    try {
      const response = await esClient.indices.exists({ index: this.INDEX_NAME });
      return response;
    } catch (error) {
      console.error('[ElasticsearchService] 索引检查失败:', error);
      return false;
    }
  }
}

import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";
import crypto from 'crypto';

// 日期格式化配置
export interface DateFormatOptions {
  locale?: string;
  format?: 'short' | 'medium' | 'long' | 'full' | 'iso';
  includeTime?: boolean;
}

// 默认日期格式配置
const DEFAULT_DATE_CONFIG: DateFormatOptions = {
  locale: 'en-US', // 默认英文，符合项目国际化方向
  format: 'medium',
  includeTime: false
};

/**
 * 统一的日期格式化函数
 * 使用浏览器原生 Intl.DateTimeFormat，确保前端、后端、导出的一致性
 */
export function formatDate(
  date: string | number | Date | null | undefined,
  options: DateFormatOptions = {}
): string {
  if (!date) return '';

  try {
    const dateObj = new Date(date);
    if (isNaN(dateObj.getTime())) return '';

    const config = { ...DEFAULT_DATE_CONFIG, ...options };

    // ISO格式特殊处理
    if (config.format === 'iso') {
      return dateObj.toISOString().split('T')[0]; // YYYY-MM-DD
    }

    // 构建 Intl.DateTimeFormat 选项
    const formatOptions: Intl.DateTimeFormatOptions = {};

    switch (config.format) {
      case 'short':
        formatOptions.dateStyle = 'short';
        break;
      case 'medium':
        formatOptions.dateStyle = 'medium';
        break;
      case 'long':
        formatOptions.dateStyle = 'long';
        break;
      case 'full':
        formatOptions.dateStyle = 'full';
        break;
    }

    if (config.includeTime) {
      formatOptions.timeStyle = 'short';
    }

    return new Intl.DateTimeFormat(config.locale, formatOptions).format(dateObj);
  } catch (__error) {
    console.warn('Date formatting error:', __error);
    return '';
  }
}

/**
 * 专用于表格显示的日期格式化
 * 统一使用简洁的中等格式
 */
export function formatTableDate(date: string | number | Date | null | undefined): string {
  return formatDate(date, { format: 'medium' });
}

/**
 * 专用于导出文件的日期格式化
 * 使用ISO格式确保跨平台兼容性
 */
export function formatExportDate(date: string | number | Date | null | undefined): string {
  return formatDate(date, { format: 'iso' });
}

/**
 * 专用于详情页显示的日期格式化
 * 使用较长格式提供更多信息
 */
export function formatDetailDate(date: string | number | Date | null | undefined): string {
  return formatDate(date, { format: 'long' });
}

/**
 * 根据用户浏览器语言自动格式化日期
 */
export function formatDateAuto(date: string | number | Date | null | undefined): string {
  const userLocale = typeof navigator !== 'undefined'
    ? navigator.language
    : 'en-US';

  return formatDate(date, { locale: userLocale, format: 'medium' });
}

// ==================== 日期检测和验证工具 ====================

/**
 * 验证字符串是否为有效日期
 * 使用严格的日期格式验证，避免将数字误识别为日期
 */
export function isValidDateString(value: unknown): boolean {
  if (!value) return false;

  // 如果已经是Date对象
  if (value instanceof Date) {
    return !isNaN(value.getTime());
  }

  // 如果是数字，只有在是合理的时间戳时才认为是日期
  if (typeof value === 'number') {
    // 时间戳应该是一个合理的范围，且不能是小整数
    // 小于 1000000000000 (2001年) 的数字很可能不是时间戳
    const minTimestamp = 1000000000000; // 2001-09-09 (合理的时间戳下限)
    const maxTimestamp = 4102444800000; // 2100-01-01
    return value >= minTimestamp && value <= maxTimestamp;
  }

  // 如果是字符串，进行严格验证
  if (typeof value === 'string') {
    const trimmed = value.trim();
    if (trimmed === '') return false;

    // 排除纯数字字符串（除非是明确的时间戳格式）
    if (/^\d+$/.test(trimmed)) {
      // 如果是纯数字，只有在长度合理时才可能是时间戳
      const num = parseInt(trimmed, 10);
      if (trimmed.length < 8) {
        // 短数字（如 "2", "123"）不应该被认为是日期
        return false;
      }
      // 长数字可能是时间戳，使用数字验证逻辑
      return isValidDateString(num);
    }

    // 检查是否符合常见的日期格式
    const datePatterns = [
      /^\d{4}-\d{1,2}-\d{1,2}$/, // YYYY-MM-DD
      /^\d{4}\/\d{1,2}\/\d{1,2}$/, // YYYY/MM/DD
      /^\d{1,2}\/\d{1,2}\/\d{4}$/, // MM/DD/YYYY
      /^\d{1,2}-\d{1,2}-\d{4}$/, // MM-DD-YYYY
      /^\d{4}-\d{1,2}-\d{1,2}T\d{1,2}:\d{1,2}/, // ISO format with time
      /^[A-Za-z]{3}\s+\d{1,2},?\s+\d{4}/, // "Jan 15, 2024" or "Jan 15 2024"
    ];

    const matchesPattern = datePatterns.some(pattern => pattern.test(trimmed));
    if (!matchesPattern) {
      return false;
    }

    // 最后验证是否能正确解析为日期
    const date = new Date(trimmed);
    return !isNaN(date.getTime());
  }

  return false;
}

/**
 * 智能检测字段是否可能是日期字段
 * 基于字段名模式和数据内容
 */
export function detectDateField(fieldName: string, sampleValues: unknown[]): {
  isLikelyDate: boolean;
  confidence: number;
  reason: string;
} {
  // 1. 基于字段名的模式匹配
  const dateFieldPatterns = [
    /date/i,
    /time/i,
    /created/i,
    /updated/i,
    /modified/i,
    /approval/i,
    /valid/i,
    /expire/i,
    /establish/i,
    /birth/i,
    /start/i,
    /end/i,
    /begin/i,
    /finish/i,
    /日期/,
    /时间/,
    /创建/,
    /更新/,
    /修改/,
    /批准/,
    /有效/,
    /过期/,
    /成立/,
    /开始/,
    /结束/
  ];

  const nameScore = dateFieldPatterns.some(pattern => pattern.test(fieldName)) ? 0.7 : 0;

  // 2. 基于数据内容的检测
  const validSamples = sampleValues.filter(v => v !== null && v !== undefined && v !== '');
  if (validSamples.length === 0) {
    return { isLikelyDate: nameScore > 0, confidence: nameScore, reason: 'Based on field name only' };
  }

  const dateValidCount = validSamples.filter(isValidDateString).length;
  const contentScore = dateValidCount / validSamples.length;

  // 3. 综合评分
  const finalScore = Math.max(nameScore, contentScore * 0.8);

  let reason = '';
  if (nameScore > 0 && contentScore > 0.5) {
    reason = 'Field name suggests date and content contains valid dates';
  } else if (nameScore > 0) {
    reason = 'Field name suggests date field';
  } else if (contentScore > 0.8) {
    reason = 'Most content appears to be valid dates';
  } else if (contentScore > 0.5) {
    reason = 'Some content appears to be dates';
  } else {
    reason = 'No strong date indicators found';
  }

  return {
    isLikelyDate: finalScore > 0.5,
    confidence: finalScore,
    reason
  };
}

/**
 * 智能日期格式化：结合配置和自动检测
 */
export function smartFormatDate(
  value: unknown,
  fieldConfig?: { fieldType?: string; fieldName?: string },
  options: DateFormatOptions = {}
): string {
  // 1. 如果配置明确指定为日期类型
  if (fieldConfig?.fieldType === 'date') {
    return formatDate(value as string | number | Date | null | undefined, options);
  }

  // 2. 如果没有配置，尝试智能检测
  if (!fieldConfig?.fieldType && fieldConfig?.fieldName) {
    const detection = detectDateField(fieldConfig.fieldName, [value]);
    if (detection.isLikelyDate && isValidDateString(value)) {
      return formatDate(value as string | number | Date | null | undefined, options);
    }
  }

  // 3. 最后尝试直接验证值本身
  if (isValidDateString(value)) {
    return formatDate(value as string | number | Date | null | undefined, options);
  }

  // 4. 不是日期，返回原始字符串
  return String(value || '');
}

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

/**
 * 生成业务唯一键的Hash值
 * @param businessKey 原始业务唯一键
 * @param algorithm 哈希算法，默认MD5
 * @returns Hash值
 */
export function generateBusinessKeyHash(
  businessKey: string, 
  algorithm: 'md5' | 'sha256' = 'md5'
): string {
  if (!businessKey || businessKey.trim() === '') {
    throw new Error('业务唯一键不能为空');
  }
  
  const hash = crypto.createHash(algorithm);
  hash.update(businessKey.trim());
  return hash.digest('hex');
}

/**
 * 验证Hash值是否有效
 * @param hash Hash值
 * @param algorithm 哈希算法
 * @returns 是否有效
 */
export function isValidHash(hash: string, algorithm: 'md5' | 'sha256' = 'md5'): boolean {
  if (!hash || typeof hash !== 'string') {
    return false;
  }
  
  const expectedLength = algorithm === 'md5' ? 32 : 64;
  const hashRegex = algorithm === 'md5' ? /^[a-f0-9]{32}$/i : /^[a-f0-9]{64}$/i;
  
  return hash.length === expectedLength && hashRegex.test(hash);
}

/**
 * 批量生成Hash值（用于大数据量处理）
 * @param businessKeys 业务唯一键数组
 * @param algorithm 哈希算法
 * @returns Hash值数组
 */
export function generateBusinessKeyHashes(
  businessKeys: string[], 
  algorithm: 'md5' | 'sha256' = 'md5'
): string[] {
  return businessKeys.map(key => generateBusinessKeyHash(key, algorithm));
}

/**
 * 生成业务唯一键（原始版本，保持向后兼容）
 * @param row 数据行
 * @param uniqueKeyRule 唯一键生成规则
 * @returns 原始业务唯一键
 */
export function generateBusinessKey(
  row: Record<string, any>, 
  uniqueKeyRule: (row: Record<string, any>) => string
): string {
  return uniqueKeyRule(row);
}

/**
 * 生成完整的业务唯一键信息（原始+Hash）
 * @param row 数据行
 * @param uniqueKeyRule 唯一键生成规则
 * @param algorithm 哈希算法
 * @returns 包含原始键和Hash的对象
 */
export function generateBusinessKeyInfo(
  row: Record<string, any>,
  uniqueKeyRule: (row: Record<string, any>) => string,
  algorithm: 'md5' | 'sha256' = 'md5'
): { businessKey: string; businessKeyHash: string } {
  const businessKey = generateBusinessKey(row, uniqueKeyRule);
  const businessKeyHash = generateBusinessKeyHash(businessKey, algorithm);
  
  return { businessKey, businessKeyHash };
}

// 通用数据转换工具函数
export function transformDataByFieldMapping(
  data: Record<string, any>, 
  fieldMapping: Record<string, string>, 
  databaseType: string
): Record<string, any> {
  const transformedData: Record<string, any> = {};
  
  // 遍历所有字段映射
  for (const [sourceField, targetField] of Object.entries(fieldMapping)) {
    // 支持多种字段名格式：英文、中文、或其他自定义格式
    const value = data[targetField] || data[sourceField] || data[sourceField.toLowerCase()] || data[sourceField.toUpperCase()];
    if (value !== undefined) {
      transformedData[targetField] = value;
    }
  }
  
  // 添加数据库类型字段
  transformedData.database = databaseType;
  
  return transformedData;
}

// 数据验证工具函数
export function validateRequiredFields(
  data: Record<string, any>, 
  requiredFields: string[]
): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  for (const field of requiredFields) {
    if (!data[field] || data[field].toString().trim() === '') {
      errors.push(`必填字段缺失: ${field}`);
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}

// 字段映射验证工具函数
export function validateFieldMapping(
  fieldMapping: Record<string, string>, 
  sampleData: Record<string, any>
): { isValid: boolean; warnings: string[] } {
  const warnings: string[] = [];
  
  // 检查是否有重复的目标字段
  const targetFields = Object.values(fieldMapping);
  const uniqueTargetFields = new Set(targetFields);
  if (targetFields.length !== uniqueTargetFields.size) {
    warnings.push('字段映射中存在重复的目标字段名');
  }
  
  // 检查样本数据中是否有未映射的字段
  const mappedSourceFields = Object.keys(fieldMapping);
  const dataFields = Object.keys(sampleData);
  const unmappedFields = dataFields.filter(field => !mappedSourceFields.includes(field));
  
  if (unmappedFields.length > 0) {
    warnings.push(`以下字段未在映射中定义: ${unmappedFields.join(', ')}`);
  }
  
  return {
    isValid: warnings.length === 0,
    warnings
  };
}

// 数据清理工具函数
export function cleanData(data: Record<string, any>): Record<string, any> {
  const cleanedData: Record<string, any> = {};
  
  for (const [key, value] of Object.entries(data)) {
    if (value !== null && value !== undefined) {
      // 如果是字符串，去除首尾空格
      if (typeof value === 'string') {
        const trimmed = value.trim();
        if (trimmed !== '') {
          cleanedData[key] = trimmed;
        }
      } else {
        cleanedData[key] = value;
      }
    }
  }
  
  return cleanedData;
}

// 数据类型转换工具函数
export function convertDataTypes(data: Record<string, any>, typeMapping: Record<string, string>): Record<string, any> {
  const convertedData: Record<string, any> = {};
  
  for (const [key, value] of Object.entries(data)) {
    const targetType = typeMapping[key];
    
    if (targetType && value !== null && value !== undefined) {
      try {
        switch (targetType) {
          case 'number':
            convertedData[key] = Number(value);
            break;
          case 'boolean':
            if (typeof value === 'string') {
              convertedData[key] = ['true', '1', '是', 'yes'].includes(value.toLowerCase());
            } else {
              convertedData[key] = Boolean(value);
            }
            break;
          case 'date':
            convertedData[key] = new Date(value);
            break;
          default:
            convertedData[key] = value;
        }
      } catch (__error) {
        // 转换失败时保持原值
        convertedData[key] = value;
      }
    } else {
      convertedData[key] = value;
    }
  }
  
  return convertedData;
}

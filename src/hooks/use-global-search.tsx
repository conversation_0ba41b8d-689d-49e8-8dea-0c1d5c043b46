import { useState, useCallback } from 'react';
import { useDebounceSearch } from '@/hooks/use-debounced-search';
import React from 'react';

export interface GlobalSearchResult {
  database: string;
  count: number;
}

export function useGlobalSearch() {
  const [results, setResults] = useState<GlobalSearchResult[]>([]);
  const [query, setQuery] = useState('');
  const [isSearching, setIsSearching] = useState(false);

  const cacheRef = React.useRef<Record<string, GlobalSearchResult[]>>({});
  const abortRef = React.useRef<AbortController | null>(null);

  // 手动搜索函数
  const performSearch = useCallback(async (searchQuery?: string) => {
    const kw = searchQuery || query;
    if (!kw.trim()) {
      setResults([]);
      return;
    }

    // If we already have cache for same kw, use it
    if (cacheRef.current[kw]) {
      setResults(cacheRef.current[kw]);
      return;
    }

    setIsSearching(true);
    try {
      abortRef.current?.abort();
      const controller = new AbortController();
      abortRef.current = controller;

      console.log('Performing global search for:', kw);
      const res = await fetch(`/api/global-search?q=${encodeURIComponent(kw)}`, {
        signal: controller.signal
      }).then((r) => r.json());

      if (res.success) {
        console.log('Global search results:', res.data);
        cacheRef.current[kw] = res.data as GlobalSearchResult[];
        setResults(res.data as GlobalSearchResult[]);
      } else {
        console.error('Global search failed:', res.error);
        setResults([]);
      }
    } catch (_e) {
      if ((_e as any)?.name !== 'AbortError') {
        console.error('global search failed', _e);
        setResults([]);
      }
    } finally {
      setIsSearching(false);
    }
  }, [query]);

  // 清空结果
  const clearResults = useCallback(() => {
    setResults([]);
  }, []);

  return {
    query,
    setQuery,
    results,
    isSearching,
    performSearch,
    clearResults
  };
}
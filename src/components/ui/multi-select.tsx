"use client"

import * as React from "react"
import { ChevronsUpDown, X } from "lucide-react"
import { cn } from "@/lib/utils"
import { But<PERSON> } from "@/components/ui/button"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { Badge } from "@/components/ui/badge"
import { Checkbox } from "@/components/ui/checkbox"

export interface MultiSelectOption {
  value: string
  label: string
  count?: number
}

interface MultiSelectProps {
  options: MultiSelectOption[]
  value: string[]
  onValueChange: (value: string[]) => void
  placeholder?: string
  emptyText?: string
  className?: string
  disabled?: boolean
  showAllOption?: boolean
}

export function MultiSelect({
  options,
  value,
  onValueChange,
  placeholder = "Select options...",
  emptyText = "No options found.",
  className,
  disabled = false,
  showAllOption = true,
}: MultiSelectProps) {
  const [open, setOpen] = React.useState(false)
  const [searchTerm, setSearchTerm] = React.useState("")

  const handleSelect = (optionValue: string) => {
    if (optionValue === '__all__') {
      // 如果点击"All"，清空所有选择
      onValueChange([])
    } else if (value.includes(optionValue)) {
      onValueChange(value.filter((v) => v !== optionValue))
    } else {
      onValueChange([...value, optionValue])
    }
  }

  const handleRemove = (optionValue: string) => {
    onValueChange(value.filter((v) => v !== optionValue))
  }

  const selectedOptions = options.filter((option) => value.includes(option.value))

  // 过滤选项：搜索 + 性能优化
  const filteredOptions = React.useMemo(() => {
    let filtered = options

    // 搜索过滤
    if (searchTerm.trim()) {
      filtered = filtered.filter(option =>
        option.label.toLowerCase().includes(searchTerm.toLowerCase()) ||
        option.value.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    // 性能优化：如果选项太多，只显示前50个 + 已选择的
    if (filtered.length > 50) {
      const selected = filtered.filter(option => value.includes(option.value))
      const unselected = filtered.filter(option => !value.includes(option.value)).slice(0, 50 - selected.length)
      filtered = [...selected, ...unselected]
    }

    return filtered
  }, [options, searchTerm, value])

  return (
    <div className={cn("w-full", className)}>
      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <Button
            variant="outline" role="combobox" aria-expanded={open}
            className="w-full justify-between h-8 text-xs" disabled={disabled}
          >
            <div className="flex flex-wrap gap-1 max-w-[calc(100%-20px)]">
              {selectedOptions.length === 0 ? (
                showAllOption ? (
                  <span className="text-foreground">All</span>
                ) : (
                  <span className="text-muted-foreground">{placeholder}</span>
                )
              ) : selectedOptions.length <= 2 ? (
                selectedOptions.map((option) => (
                  <Badge
                    key={option.value}
                    variant="secondary" className="text-xs">
                    {option.label}
                    {option.count !== undefined && ` (${option.count})`}
                  </Badge>
                ))
              ) : (
                <Badge variant="secondary" className="text-xs">
                  {selectedOptions.length} selected
                </Badge>
              )}
            </div>
            <ChevronsUpDown className="ml-2 h-3 w-3 shrink-0 opacity-50" />
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-full p-0" align="start">
          {/* 搜索框 - 当选项超过10个时显示 */}
          {options.length > 10 && (
            <div className="p-2 border-b">
              <input
                type="text" placeholder="Search options..." value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full px-2 py-1 text-xs border rounded focus:outline-none focus:ring-1 focus:ring-blue-500" />
            </div>
          )}
          <div className="max-h-64 overflow-auto">
            {filteredOptions.length === 0 ? (
              <div className="py-6 text-center text-sm text-muted-foreground">
                {searchTerm ? `No options found for "${searchTerm}"` : emptyText}
              </div>
            ) : (
              <div className="p-1">
                {showAllOption && (
                  <div
                    key="__all__" className="flex items-center space-x-2 rounded-sm px-2 py-1.5 text-sm cursor-pointer hover:bg-accent hover:text-accent-foreground" onClick={() => handleSelect('__all__')}
                  >
                    <Checkbox
                      checked={value.length === 0}
                      onCheckedChange={() => handleSelect('__all__')}
                      className="h-4 w-4" />
                    <div className="flex-1 flex items-center justify-between">
                      <span>All</span>
                    </div>
                  </div>
                )}
                {filteredOptions.map((option) => (
                  <div
                    key={option.value}
                    className="flex items-center space-x-2 rounded-sm px-2 py-1.5 text-sm cursor-pointer hover:bg-accent hover:text-accent-foreground" onClick={() => handleSelect(option.value)}
                  >
                    <Checkbox
                      checked={value.includes(option.value)}
                      onCheckedChange={() => handleSelect(option.value)}
                      className="h-4 w-4" />
                    <div className="flex-1 flex items-center justify-between">
                      <span>{option.label}</span>
                      {option.count !== undefined && (
                        <Badge variant="outline" className="ml-2 text-xs">
                          {option.count}
                        </Badge>
                      )}
                    </div>
                  </div>
                ))}
                {/* 显示限制提示 */}
                {options.length > 50 && !searchTerm && (
                  <div className="px-2 py-1 text-xs text-muted-foreground border-t">
                    Showing top 50 options. Use search to find more.
                  </div>
                )}
              </div>
            )}
          </div>
        </PopoverContent>
      </Popover>

      {/* 显示已选择的标签 */}
      {selectedOptions.length > 0 && (
        <div className="flex flex-wrap gap-1 mt-2">
          {selectedOptions.map((option) => (
            <Badge
              key={option.value}
              variant="secondary" className="text-xs cursor-pointer hover:bg-destructive hover:text-destructive-foreground" onClick={() => handleRemove(option.value)}
            >
              {option.label}
              {option.count !== undefined && ` (${option.count})`}
              <X className="ml-1 h-3 w-3" />
            </Badge>
          ))}
        </div>
      )}
    </div>
  )
}
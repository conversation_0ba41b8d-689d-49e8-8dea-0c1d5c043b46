"use client";

import { useState, useRef, useEffect } from "react";
import { Calendar } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";

interface DateRangePickerProps {
  startDate?: string;
  endDate?: string;
  onStartDateChange: (date: string) => void;
  onEndDateChange: (date: string) => void;
  placeholder?: string;
  className?: string;
}

export function DateRangePicker({
  startDate = "",
  endDate = "",
  onStartDateChange,
  onEndDateChange,
  placeholder = "Select date range",
  className =""
}: DateRangePickerProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [tempStartDate, setTempStartDate] = useState(startDate);
  const [tempEndDate, setTempEndDate] = useState(endDate);
  const containerRef = useRef<HTMLDivElement>(null);

  // 格式化显示文本
  const getDisplayText = () => {
    if (startDate && endDate) {
      return `${startDate} - ${endDate}`;
    } else if (startDate) {
      return `${startDate} - `;
    } else if (endDate) {
      return ` - ${endDate}`;
    }
    return "";
  };

  // 点击外部关闭
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isOpen]);

  // 应用选择的日期
  const handleApply = () => {
    onStartDateChange(tempStartDate);
    onEndDateChange(tempEndDate);
    setIsOpen(false);
  };

  // 清空日期
  const handleClear = () => {
    setTempStartDate("");
    setTempEndDate("");
    onStartDateChange("");
    onEndDateChange("");
    setIsOpen(false);
  };

  return (
    <div ref={containerRef} className={`relative ${className}`}>
      {/* 输入框 */}
      <div className="relative">
        <Input
          value={getDisplayText()}
          onChange={(e) => {
            const value = e.target.value;
            // 简单的日期范围解析 "YYYY-MM-DD - YYYY-MM-DD"
            const parts = value.split(' - ');
            if (parts.length === 2) {
              onStartDateChange(parts[0].trim());
              onEndDateChange(parts[1].trim());
            } else if (parts.length === 1 && parts[0].includes('-') && parts[0].length >= 10) {
              onStartDateChange(parts[0].trim());
            }
          }}
          onFocus={() => setIsOpen(true)}
          placeholder={placeholder}
          className="w-full text-xs h-8 border-gray-300 focus:border-blue-500 pr-8" />
        <Calendar
          className="absolute right-2 top-1/2 transform -translate-y-1/2 h-3 w-3 text-gray-400 cursor-pointer" onClick={() => setIsOpen(!isOpen)}
        />
      </div>

      {/* 弹出的日期选择面板 */}
      {isOpen && (
        <div className="fixed bg-white border border-gray-300 rounded-md shadow-xl z-[9999] p-4 min-w-[400px]" style={{
               top: containerRef.current ? containerRef.current.getBoundingClientRect().bottom + window.scrollY + 4 : 0,
               left: containerRef.current ? containerRef.current.getBoundingClientRect().left + window.scrollX : 0
             }}>
          <div className="flex gap-4">
            {/* Start date calendar */}
            <div className="flex-1">
              <label className="block text-xs font-medium text-gray-600 mb-2">
                Start Date
              </label>
              <Input
                type="date" value={tempStartDate}
                onChange={(e) => setTempStartDate(e.target.value)}
                className="w-full text-xs h-8 border-gray-300 focus:border-blue-500" />
            </div>

            {/* End date calendar */}
            <div className="flex-1">
              <label className="block text-xs font-medium text-gray-600 mb-2">
                End Date
              </label>
              <Input
                type="date" value={tempEndDate}
                onChange={(e) => setTempEndDate(e.target.value)}
                className="w-full text-xs h-8 border-gray-300 focus:border-blue-500" />
            </div>
          </div>

          {/* Action buttons */}
          <div className="flex justify-between items-center mt-4 pt-3 border-t border-gray-200">
            <Button
              variant="ghost" size="sm" onClick={handleClear}
              className="text-xs text-gray-500 hover:text-gray-700">
              Clear
            </Button>
            <div className="flex gap-2">
              <Button
                variant="outline" size="sm" onClick={() => setIsOpen(false)}
                className="text-xs">
                Cancel
              </Button>
              <Button
                size="sm" onClick={handleApply}
                className="text-xs bg-blue-600 hover:bg-blue-700">
                Confirm
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

import { type NextRequest, NextResponse } from 'next/server';
import { esClient } from '@/lib/elasticsearch';

export const dynamic = 'force-dynamic';

/**
 * GET /api/debug-es?action=<action>&query=<query>&database=<database>
 * 调试 Elasticsearch 索引的工具 API
 */
export async function GET(req: NextRequest): Promise<Response> {
  try {
    const searchParams = req.nextUrl.searchParams;
    const action = searchParams.get('action') || 'info';
    const query = searchParams.get('query')?.trim();
    const database = searchParams.get('database')?.trim();

    switch (action) {
      case 'info':
        return await getIndexInfo();
      
      case 'sample':
        return await getSampleData(database);
      
      case 'search':
        if (!query) {
          return NextResponse.json({ error: 'Query parameter required for search' }, { status: 400 });
        }
        return await debugSearch(query, database);
      
      case 'count':
        return await getCountByDatabase();
      
      case 'mapping':
        return await getIndexMapping();
      
      default:
        return NextResponse.json({ 
          error: 'Invalid action',
          availableActions: ['info', 'sample', 'search', 'count', 'mapping']
        }, { status: 400 });
    }
  } catch (error) {
    console.error('Debug ES error:', error);
    return NextResponse.json({ 
      error: 'Debug failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 });
  }
}

// 获取索引基本信息
async function getIndexInfo() {
  try {
    const response = await esClient.indices.stats({
      index: 'medical_index'
    });

    const stats = (response as any).body || response;
    const indexStats = stats.indices?.medical_index;

    return NextResponse.json({
      success: true,
      data: {
        indexExists: !!indexStats,
        documentCount: indexStats?.total?.docs?.count || 0,
        indexSize: indexStats?.total?.store?.size_in_bytes || 0,
        shards: indexStats?.shards || {}
      }
    });
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: 'Failed to get index info',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}

// 获取样本数据
async function getSampleData(database?: string) {
  try {
    const searchBody: any = {
      size: 5,
      _source: ['id', 'table_code', 'registration_no', 'product_combined', 'company_combined']
    };

    if (database) {
      searchBody.query = {
        term: { 'table_code': database }
      };
    }

    const response = await esClient.search({
      index: 'medical_index',
      body: searchBody
    });

    const responseBody = (response as any).body || response;
    const hits = responseBody.hits?.hits || [];

    return NextResponse.json({
      success: true,
      data: {
        total: responseBody.hits?.total?.value || 0,
        samples: hits.map((hit: any) => ({
          id: hit._id,
          source: hit._source
        }))
      }
    });
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: 'Failed to get sample data',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}

// 调试搜索
async function debugSearch(query: string, database?: string) {
  try {
    const mustQueries = [];
    
    // 主搜索查询
    mustQueries.push({
      multi_match: {
        query: query,
        fields: [
          'registration_no.*',
          'product_combined.*', 
          'company_combined.*'
        ],
        type: 'best_fields',
        fuzziness: 'AUTO'
      }
    });

    // 数据库过滤
    if (database) {
      mustQueries.push({
        term: { 'table_code': database }
      });
    }

    const searchBody = {
      query: {
        bool: {
          must: mustQueries
        }
      },
      size: 10,
      track_total_hits: true,
      _source: ['id', 'table_code', 'registration_no', 'product_combined', 'company_combined'],
      explain: true // 添加评分解释
    };

    console.log('Debug search body:', JSON.stringify(searchBody, null, 2));

    const response = await esClient.search({
      index: 'medical_index',
      body: searchBody
    });

    const responseBody = (response as any).body || response;
    const hits = responseBody.hits?.hits || [];
    const total = responseBody.hits?.total?.value || 0;

    return NextResponse.json({
      success: true,
      data: {
        query: searchBody,
        total: total,
        results: hits.map((hit: any) => ({
          id: hit._id,
          score: hit._score,
          source: hit._source,
          explanation: hit._explanation
        }))
      }
    });
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: 'Debug search failed',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}

// 按数据库统计文档数量
async function getCountByDatabase() {
  try {
    const searchBody = {
      size: 0,
      aggs: {
        databases: {
          terms: {
            field: 'table_code',
            size: 100
          }
        }
      }
    };

    const response = await esClient.search({
      index: 'medical_index',
      body: searchBody
    });

    const responseBody = (response as any).body || response;
    const buckets = responseBody.aggregations?.databases?.buckets || [];

    return NextResponse.json({
      success: true,
      data: {
        total: responseBody.hits?.total?.value || 0,
        byDatabase: buckets.map((bucket: any) => ({
          database: bucket.key,
          count: bucket.doc_count
        }))
      }
    });
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: 'Failed to get count by database',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}

// 获取索引映射
async function getIndexMapping() {
  try {
    const response = await esClient.indices.getMapping({
      index: 'medical_index'
    });

    const mapping = (response as any).body || response;

    return NextResponse.json({
      success: true,
      data: mapping
    });
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: 'Failed to get index mapping',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}
